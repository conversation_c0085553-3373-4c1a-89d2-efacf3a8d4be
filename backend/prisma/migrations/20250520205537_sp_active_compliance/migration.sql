/*
 Warnings:
 
 - You are about to drop the column `is_active` on the `checklist` table. All the data in the column will be lost.
 
 */
-- AlterTable
ALTER TABLE "checklist" DROP COLUMN "is_active",
  ADD COLUMN "is_active_sp" BOOLEAN NOT NULL DEFAULT true;
-- AlterTable
ALTER TABLE "team_member_invitations"
ALTER COLUMN "expires_at"
SET DEFAULT NOW() + interval '7 days';
INSERT INTO checklist (id, name)
VALUES (uuid_generate_v4(), 'Interview', is_active_sp),
  (uuid_generate_v4(), 'App Assessment', true),
  (
    uuid_generate_v4(),
    'Document Verification',
    true
  ),
  (uuid_generate_v4(), 'Training', false),
  (uuid_generate_v4(), 'Soft Skills Training', true),
  (uuid_generate_v4(), 'App Training', true),
  (
    uuid_generate_v4(),
    'T-shirt and Bag Handed Over',
    true
  ),
  (uuid_generate_v4(), 'Toolkit Assigned', true);
INSERT INTO checklist (id, name, is_active_sp)
VALUES (uuid_generate_v4(), 'Rating', true);