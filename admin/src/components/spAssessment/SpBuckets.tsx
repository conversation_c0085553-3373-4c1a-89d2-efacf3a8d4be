import { <PERSON><PERSON>, <PERSON>confirm, Table, notification } from "antd";
import Search from "antd/es/input/Search";
import dayjs from "dayjs";
import React, { useCallback, useState } from "react";
import { BsBoxes } from "react-icons/bs";
import { Outlet, useNavigate, useSearchParams } from "react-router-dom";
import {
  AssignmentUserType,
  GetAllBucketsDocument,
  useDeleteBucketMutation,
  useGetAllBucketsQuery
} from "../../__generated__";
import ErrorComponent from "../../components/error/Error";
import Loading from "../../components/loading/Loading";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import { debounce } from "../../utils/utils";

const SpBuckets: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [searchParams, setSearchParams] = useSearchParams();

  const navigate = useNavigate();

  //Query
  const {
    data: buckets,
    loading,
    error
  } = useGetAllBucketsQuery({
    variables: {
      search: searchParams.get("search") || undefined,
      pagination: {
        take: pageSize,
        skip: currentPage - 1
      },
      data: {
        assign_to: AssignmentUserType.ServiceProvider
      }
    }
  });

  //Mutation
  const [deleteBucket, { loading: deleteLoading }] = useDeleteBucketMutation();

  // Calculate the total number of data pages based on search results
  const totalDataPages = Math.ceil(buckets?.getAllBuckets?.total_count || 0 / pageSize);

  //Handle Debounce Search
  const debounceSearch = useCallback(
    debounce((val: string) => {
      setCurrentPage(1);
      setSearchParams({ search: val });
    }, 500),
    []
  );

  const handleDeleteBucket = async (id: string) => {
    try {
      await deleteBucket({
        variables: {
          deleteBucketId: id
        },
        onCompleted() {
          notification.success({
            message: "Bucket deleted successfully"
          });
        },
        onError(err) {
          notification.error({
            message: err?.message
          });
        },
        refetchQueries: [GetAllBucketsDocument]
      });
    } catch (err) {
      console.log(err);
    }
  };

  const handleCurrentPage = (page: number, pageSize: number) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };

  if (error) {
    return <ErrorComponent error={error} />;
  }
  if (deleteLoading) {
    return <Loading tip="Loading ..." />;
  }

  return (
    <ProtectedRoute disableChildAuth parent_url="/dashboard/sp/assignment/buckets">
      {(permissions) => (
        <div className="p-3">
          <div className="flex items-center gap-3 bg-white border-purple-400 shadow-sm p-2 rounded-md border border-solid border-l-4 border-r-0 border-b-0 border-t-0">
            <BsBoxes size={32} className="text-colorPrimary" />
            <div className="text-2xl">Buckets</div>
          </div>
          <div className="flex justify-between pt-3">
            <div className="w-1/3 ml-3">
              <Search
                placeholder="Search buckets"
                allowClear
                defaultValue={searchParams.get("search") || ""}
                onChange={(e) => {
                  debounceSearch(e.target.value);
                }}
              />
            </div>
            <Button
              type="primary"
              disabled={!permissions.create}
              onClick={() => {
                navigate("/dashboard/sp/assignment/buckets/new");
              }}
              className="mr-3"
            >
              Add Bucket
            </Button>
          </div>
          <Table
            loading={loading}
            className="mt-2"
            bordered
            columns={[
              {
                title: "Bucket name",
                dataIndex: "name",
                key: "bucket_name",
                align: "center"
              },
              {
                title: "Total questions",
                dataIndex: "total_questions",
                key: "total_questions",
                align: "center"
              },
              {
                title: "Created on",
                dataIndex: "created_on",
                key: "created_on",
                align: "center"
              },
              {
                title: "Actions",
                align: "center",
                render: (data) => (
                  <div>
                    <Button
                      disabled={!permissions.update}
                      className="mr-1"
                      color="blue"
                      onClick={() => {
                        navigate(`/dashboard/sp/assignment/buckets/${data?.id}`);
                      }}
                    >
                      Edit
                    </Button>
                    <Button.Group>
                      <Popconfirm
                        placement="left"
                        showCancel={true}
                        title={
                          <>
                            <p>Are you sure to delete bucket?</p>
                          </>
                        }
                        onConfirm={() => {
                          handleDeleteBucket(data?.id);
                        }}
                        okText="Confirm"
                        okButtonProps={{
                          loading: deleteLoading
                        }}
                        cancelText="Cancel"
                      >
                        <Button disabled={!permissions.delete} danger>
                          Delete
                        </Button>
                      </Popconfirm>
                    </Button.Group>
                  </div>
                )
              }
            ]}
            rowKey={"id"}
            dataSource={buckets?.getAllBuckets?.data?.map((b) => ({
              name: b?.name,
              total_questions: b?.total_question,
              created_on: dayjs(b?.created_at).format("hh:mm A, MMM DD YYYY "),
              id: b?.id
            }))}
            pagination={{
              current: currentPage,
              pageSize,
              onChange: (page, pageSize) => {
                handleCurrentPage(page, pageSize);
              },

              total: totalDataPages || 1
            }}
          />
          <Outlet />
        </div>
      )}
    </ProtectedRoute>
  );
};

export default SpBuckets;
