import {
  Button,
  DatePicker,
  Descriptions,
  Input,
  Popconfirm,
  Switch,
  Tabs,
  Tag,
  Typography,
  notification
} from "antd";
import { RangePickerProps } from "antd/es/date-picker";
import dayjs from "dayjs";
import React, { useState } from "react";
import { useParams } from "react-router-dom";
import {
  FeedBackState,
  GetUserInterviewDetailsForAdminDocument,
  InterviewState,
  InterviewType,
  UpdateInterviewType,
  useGetUserInterviewDetailsForAdminQuery,
  useUpdateInterviewModeOfUserMutation,
  useUpdateUserInterviewDetailsMutation
} from "../../__generated__";
import { ProtectedRoute } from "../../hooks/useSecureRoutePermissions";
import InterviewFeedbackOfAUser from "../interview/InterviewFeedbackOfUser";
import Loading from "../loading/Loading";
const { Paragraph } = Typography;

const InterviewDetailsSp: React.FC = () => {
  //Hooks
  const [interviewUpdateReason, setInterviewUpdateReason] = useState("");
  const [showCancelAndUpdateButton, setShowCancelAndUpdateButton] = useState<boolean>(false);
  const [selectedInterviewTime, setSelectedInterviewTime] = useState<RangePickerProps["value"]>();
  //Params
  const { user_id, interview_id } = useParams();

  const { data, loading, error } = useGetUserInterviewDetailsForAdminQuery({
    fetchPolicy: "no-cache",
    variables: {
      interviewId: Number(interview_id)
    },
    onCompleted(data) {
      if (
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Cancelled ||
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Completed ||
        data?.getUserInterviewDetailsForAdmin?.status === InterviewState.Expired
      ) {
        setShowCancelAndUpdateButton(true);
      } else {
        setShowCancelAndUpdateButton(false);
      }
    }
  });
  const userInterviewDetails = data?.getUserInterviewDetailsForAdmin;

  //GraphQl Mutation
  const [updateInterviewDetails] = useUpdateUserInterviewDetailsMutation();
  const [updateInterviewMode, { loading: updateInterviewModeLoading }] =
    useUpdateInterviewModeOfUserMutation();

  //👇🏻 All Functions below this 👇🏻
  //Handle Interview Cancel
  const handleInterviewCancellation = async (eventId: string) => {
    try {
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Cancel,
            user_id: Number(user_id),
            event_id: eventId,
            interview_status: InterviewState.Cancelled,
            is_active: false
          }
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        onCompleted() {
          notification.success({ message: "Interview Cancelled Successfully" });
        },
        refetchQueries: [GetUserInterviewDetailsForAdminDocument]
      });
    } catch (error) {
      console.error(error);
    }
  };

  //Handle Interview Update
  const handleInterviewUpdate = async (eventId: string) => {
    try {
      const startDate = selectedInterviewTime?.[0];
      const endDate = selectedInterviewTime?.[1];
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Update,
            user_id: Number(user_id),
            event_id: eventId,
            start: startDate,
            end: endDate,
            interview_status: InterviewState.Rescheduled,
            is_active: true,
            ...(interviewUpdateReason ? { note: interviewUpdateReason } : {})
          }
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        onCompleted() {
          notification.success({ message: "Interview Updated Successfully" });
        },
        refetchQueries: [GetUserInterviewDetailsForAdminDocument]
      });
    } catch (error) {
      console.error(error);
    }
  };
  const handleCompleteInterview = async (eventId: string) => {
    try {
      await updateInterviewDetails({
        variables: {
          data: {
            update_type: UpdateInterviewType.Update,
            user_id: Number(user_id),
            event_id: eventId,
            interview_status: InterviewState.Completed,
            is_active: false,
            ...(interviewUpdateReason ? { note: interviewUpdateReason } : {})
          }
        },
        onError(error) {
          notification.error({ message: error.message });
        },
        onCompleted() {
          notification.success({ message: "Interview Updated Successfully" });
        },
        refetchQueries: [GetUserInterviewDetailsForAdminDocument]
      });
    } catch (error) {
      console.error(error);
    }
  };

  if (loading) {
    return <Loading tip="Loading..." />;
  }
  if (error) {
    return <></>;
  }

  return (
    <ProtectedRoute>
      {(permissions) => (
        <Tabs animated className="pb-3 text-lg font-bold">
          <Tabs.TabPane tab={"Interview"} key={"interview"}>
            <Descriptions bordered column={2}>
              <Descriptions.Item span={6} label="Scheduled By">
                {userInterviewDetails?.scheduled_by?.name || "User"}
              </Descriptions.Item>
              <Descriptions.Item label="Interview date and time">
                {" "}
                {dayjs(userInterviewDetails?.start_time).format("dddd, MMMM D, YYYY h:mm A")}
                {"-"}
                {dayjs(userInterviewDetails?.end_time).format("h:mm A")}
              </Descriptions.Item>
              <Descriptions.Item label="Created on">
                {dayjs(userInterviewDetails?.created_at).format("dddd, MMMM D, YYYY h:mm A")}
              </Descriptions.Item>
              <Descriptions.Item label="Feedback status">
                <Tag
                  color={
                    userInterviewDetails?.feedback?.interviewer_feedback_state ===
                    FeedBackState.Completed
                      ? "green"
                      : "red"
                  }
                >
                  {userInterviewDetails?.feedback?.interviewer_feedback_state || ""}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Feedback Link">
                <Button
                  type="link"
                  onClick={() => {
                    window.open(
                      `/feedbacks/submit/${
                        userInterviewDetails?.feedback?.feedback_form_id || "Invalid"
                      }`
                    );
                  }}
                >
                  {userInterviewDetails?.feedback?.feedback_form_id ? "Open" : ""}
                </Button>
              </Descriptions.Item>

              <Descriptions.Item label="Interviewer Name " span={1}>
                {userInterviewDetails?.interviewer?.name || "Not Available"}
              </Descriptions.Item>
              <Descriptions.Item label="Interviewer Email" span={1}>
                {userInterviewDetails?.interviewer?.email || "Not Available"}
              </Descriptions.Item>
              <Descriptions.Item label="Meet Link">
                {userInterviewDetails?.interview_type === InterviewType.InterviewOnline ? (
                  <Paragraph copyable>
                    {`https://meet.google.com/${userInterviewDetails?.google_meet_link}`}
                  </Paragraph>
                ) : (
                  `-`
                )}
              </Descriptions.Item>
              <Descriptions.Item label="Mode" span={2}>
                <Switch
                  disabled={!permissions.update}
                  checkedChildren={"Online"}
                  unCheckedChildren={"Offline"}
                  defaultChecked={
                    userInterviewDetails?.interview_type === InterviewType.InterviewOnline
                      ? true
                      : false
                  }
                  loading={updateInterviewModeLoading}
                  onChange={async (checked: boolean) => {
                    await updateInterviewMode({
                      variables: {
                        data: {
                          interview_id: Number(interview_id),
                          is_online: checked ? true : false
                        }
                      },
                      refetchQueries: [GetUserInterviewDetailsForAdminDocument]
                    });
                  }}
                />
              </Descriptions.Item>

              <Descriptions.Item label="Actions">
                <div className="flex gap-2">
                  <Popconfirm
                    showCancel={false}
                    disabled={showCancelAndUpdateButton}
                    title={
                      <>
                        <p>Are you sure to Cancel The Interview</p>
                        <Input
                          placeholder="Cancellation Reason"
                          value={interviewUpdateReason}
                          onChange={(e) => {
                            setInterviewUpdateReason(e.target.value);
                          }}
                        />
                      </>
                    }
                    onConfirm={() =>
                      handleInterviewCancellation(userInterviewDetails?.google_meet_link as string)
                    }
                    okText="Confirm"
                    cancelText="Cancel"
                  >
                    <Button
                      disabled={showCancelAndUpdateButton || !permissions.update}
                      className="cursor-pointer text-red-600 py-1 px-2 rounded-md"
                    >
                      Cancel
                    </Button>
                  </Popconfirm>
                  <Popconfirm
                    disabled={showCancelAndUpdateButton}
                    showCancel={false}
                    title={
                      <>
                        <p>Complete Interview</p>
                        <Input
                          value={interviewUpdateReason}
                          onChange={(e) => {
                            setInterviewUpdateReason(e.target.value);
                          }}
                          placeholder="Feedback"
                        />
                      </>
                    }
                    onConfirm={() =>
                      handleCompleteInterview(userInterviewDetails?.google_meet_link as string)
                    }
                    okText="Confirm"
                  >
                    <Button
                      disabled={showCancelAndUpdateButton || !permissions.update}
                      className="cursor-pointer  text-green-600 py-1 px-2 rounded-md"
                    >
                      Complete
                    </Button>
                  </Popconfirm>

                  <Popconfirm
                    showCancel={false}
                    disabled={showCancelAndUpdateButton || !permissions.update}
                    title={
                      <>
                        <p>Reschedule Interview</p>
                        <DatePicker.RangePicker
                          onChange={(time) => setSelectedInterviewTime(time)}
                          showTime={{ format: "HH:mm", use12Hours: true }}
                        />
                      </>
                    }
                    onConfirm={() =>
                      handleInterviewUpdate(userInterviewDetails?.google_meet_link as string)
                    }
                    okText="Confirm"
                  >
                    <Button
                      disabled={showCancelAndUpdateButton || !permissions.update}
                      className="cursor-pointer  text-purple-600 py-1 px-2 rounded-md"
                    >
                      Reschedule
                    </Button>
                  </Popconfirm>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="Comments" span={1}>
                {userInterviewDetails?.note || "-"}
              </Descriptions.Item>
            </Descriptions>
          </Tabs.TabPane>
          <Tabs.TabPane
            tab={"Feedback"}
            key={"feedback"}
            disabled={
              userInterviewDetails?.feedback?.interviewer_feedback_state === FeedBackState.Pending
                ? true
                : false
            }
          >
            {userInterviewDetails?.feedback?.interviewer_feedback_state ===
              FeedBackState.Completed &&
              userInterviewDetails?.feedback?.id && (
                <InterviewFeedbackOfAUser feedback_id={userInterviewDetails?.feedback.id} />
              )}
          </Tabs.TabPane>
        </Tabs>
      )}
    </ProtectedRoute>
  );
};

export default InterviewDetailsSp;
