query GetAllPolicies {
  getAllPolicies {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
      created_at
      updated_at
    }
  }
}

query GetPolicyById($id: String!) {
  getPolicyById(id: $id) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
    }
  }
}

query GetPolicyWithUserStatus($policyId: String!) {
  getPolicyWithUserStatus(policy_id: $policyId) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active

      updated_at
      user_status {
        user_id
        user_name
        accepted
        accepted_on
      }
    }
  }
}

mutation UpdatePolicyAcceptance($data: PolicyAcceptInput!) {
  updatePolicyAcceptance(data: $data) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
    }
  }
}

query GetPoliciesWithUserStatus($userId: Int!) {
  getPoliciesWithUserStatus(user_id: $userId) {
    result
    message
    data {
      id
      policy_name
      policy_name_hindi
      url
      is_active
      user_policy_tracking {
        created_at
        accepted
      }
    }
  }
}

query GetUserPolicyTracking(
  $userType: String
  $search: String
  $pagination: Pagination
  $filter: PolicyTrackingFilter
) {
  getUserPolicyTracking(
    userType: $userType
    search: $search
    pagination: $pagination
    filter: $filter
  ) {
    result
    message
    data {
      users {
        id
        name
        onboarded_date
        policies_accepted
        total_policies
        last_accepted_date
        all_accepted
        user_onboarding_data {
          meta
        }
      }
      totalCount
      totalOnboardedUsers
      usersWithAllPoliciesAccepted
    }
  }
}

mutation ExportUserPolicyTrackingData($data: ExportUserPolicyTrackingInput) {
  exportUserPolicyTrackingData(data: $data) {
    result
    message
    sheet_id
  }
}
