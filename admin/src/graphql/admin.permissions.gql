mutation CreateAdminRole($data: AdminRoleInput) {
  createAdminRole(data: $data) {
    message
    result
  }
}
mutation UpdateAdminRole($data: UpdateAdminRoleInput!) {
  updateAdminRole(data: $data) {
    message
    result
  }
}
mutation DeleteAdminRole($adminRoleId: Int!) {
  deleteAdminRole(admin_role_id: $adminRoleId) {
    message
    result
  }
}
mutation AssignRoleToAdmin($adminId: Int!, $adminRoleId: Int!) {
  assignRoleToAdmin(admin_id: $adminId, admin_role_id: $adminRoleId) {
    message
    result
  }
}
mutation PublishModule($moduleId: Int!, $rolePermission: [RolePermissionData!]!) {
  publishModule(module_id: $moduleId, role_permission: $rolePermission) {
    message
    result
  }
}

query GetAllAdminRoles {
  getAllAdminRoles {
    result
    message
    data {
      admin_role_id
      name
      description
      is_root_admin
      has_admin_privileges
      can_reject
      can_ban
      can_accept
      can_download
      created_at
      updated_at
      creator_id
      updated_by
      admin_role_permission {
        id
        admin_role_id
        feature_id

        permission_for_user
        permission_for_sp
        admin_protected_feature {
          feature_id
          is_module
          parent_module_id
          module_id
          parent_module_name
          module_name
          sub_module_name
          sub_module_url_sp
          sub_module_url_user
          icon_name
          section_id
          created_at
          is_active
          updated_at
        }
      }
    }
  }
}
query GetUnpublishedModules {
  getUnpublishedModules {
    result
    message
    data {
      module_id
      module_name
      parent_module_name
      parent_module_id
      is_published
      is_active
      icon_name
      visible_in_sp
      visible_in_users

      published_at
      published_by
      submodules {
        feature_id
        is_module
        parent_module_id
        module_id
        parent_module_name
        module_name
        sub_module_name
        sub_module_url_user
        sub_module_url_sp
        icon_name
        section_id
        created_at
        is_active
        updated_at
      }
    }
  }
}
query GetUnpublishedModuleById($moduleId: Int!) {
  getUnpublishedModuleById(module_id: $moduleId) {
    result
    message
    data {
      module_id
      module_name
      parent_module_name
      parent_module_id
      is_published
      is_active
      icon_name
      visible_in_sp
      visible_in_users

      published_at
      published_by
      submodules {
        feature_id
        is_module
        parent_module_id
        module_id
        parent_module_name
        module_name
        sub_module_name
        sub_module_url_user
        sub_module_url_sp
        icon_name
        section_id
        created_at
        is_active
        updated_at
      }
    }
  }
}
query GetAdminRoleByRoleId($adminRoleId: Int!) {
  getAdminRoleByRoleId(admin_role_id: $adminRoleId) {
    result
    message
    data {
      admin_role_id
      name
      description
      is_root_admin
      has_admin_privileges
      can_reject
      can_ban
      can_accept
      can_download
      created_at
      updated_at
      creator_id
      updated_by
      admin_role_permission {
        id
        admin_role_id
        feature_id

        permission_for_user
        permission_for_sp
        admin_protected_feature {
          feature_id
          is_module
          parent_module_id
          module_id
          parent_module_name
          module_name
          sub_module_name
          sub_module_url_user
          sub_module_url_sp
          icon_name
          section_id
          created_at
          is_active
          updated_at
        }
      }
    }
  }
}
mutation CreateNewModule($module: NewModuleData!, $features: [NewFeatureData]!) {
  createNewModule(module: $module, features: $features) {
    result
    message
  }
}
mutation CreateNewPage($page: [NewFeatureData]!, $moduleId: Int!) {
  createNewPage(page: $page, module_id: $moduleId) {
    result
    message
  }
}
