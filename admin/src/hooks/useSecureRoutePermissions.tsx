import { LoadingOutlined } from "@ant-design/icons";
import { useEffect, useState } from "react";
import { decodeToken } from "react-jwt";
import { Navigate, useLocation } from "react-router-dom";

const useSecureRoutePermissions = ({
  disableChildAuth = false,
  parent_url = ""
}: {
  disableChildAuth?: boolean;
  parent_url?: string;
} = {}) => {
  const [isLoading, setIsLoading] = useState(true); // New state
  const [permissions, setPermissions] = useState({
    read: false,
    create: false,
    update: false,
    delete: false,

    adminData: {
      is_root_admin: false,
      admin_role_id: 1,
      name: "",
      description: "",
      has_admin_privileges: false,
      can_reject: false,
      can_ban: false,
      can_download: false,
      can_accept: false,
      created_at: "",
      updated_at: null,
      creator_id: "",
      updated_by: null
    }
  });
  const location = useLocation();
  useEffect(() => {
    setIsLoading(true);
    const permissionsData = localStorage.getItem("permissionsToken");
    if (!permissionsData) return;

    const { adminPermissions, adminData }: any = decodeToken(permissionsData);

    const currentPath = location;
    const isSPRoute = currentPath.pathname.includes("/sp");

    if (isSPRoute) {
      const perm =
        disableChildAuth && parent_url && parent_url !== ""
          ? adminPermissions?.filter(({ sub_module_url_sp }: { sub_module_url_sp: string }) => {
              const parent_url_regex = new RegExp(parent_url);
              const isMatch = parent_url_regex.test(sub_module_url_sp);
              if (isMatch) {
                return currentPath.pathname.startsWith(sub_module_url_sp);
              }
            })
          : adminPermissions?.find(({ sub_module_url_sp }: { sub_module_url_sp: string }) => {
              const normalizedSubModuleUrl = sub_module_url_sp
                ?.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&")
                .replace(/:[^/]+/g, "[^/]+");

              const regex = new RegExp(`^${normalizedSubModuleUrl}$`);

              return regex.test(currentPath.pathname);
            });

      const permission = perm?.length
        ? perm?.sort(
            (a: { sub_module_url_user: string }, b: { sub_module_url_user: string }) =>
              b.sub_module_url_user.length - a.sub_module_url_user.length
          )[0]
        : perm;
      if (permission) {
        setPermissions((prev) => ({
          ...prev,
          create: permission?.permission_for_sp?.create,
          read: permission?.permission_for_sp?.read,
          update: permission?.permission_for_sp?.update,
          delete: permission?.permission_for_sp?.delete,
          adminData: adminData
        }));

        setIsLoading(false);
      } else {
        setPermissions((prev) => ({
          ...prev,
          create: false,
          read: false,
          update: false,
          delete: false,
          adminData: adminData
        }));
        setIsLoading(false);
      }
    } else {
      const perm =
        disableChildAuth && parent_url && parent_url !== ""
          ? adminPermissions?.filter(({ sub_module_url_user }: { sub_module_url_user: string }) => {
              const parent_url_regex = new RegExp(parent_url);
              const isMatch = parent_url_regex.test(sub_module_url_user);
              if (isMatch) {
                return currentPath.pathname.startsWith(sub_module_url_user);
              }
            })
          : adminPermissions?.find(({ sub_module_url_user }: { sub_module_url_user: string }) => {
              const normalizedSubModuleUrl = sub_module_url_user
                ?.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&")
                .replace(/:[^/]+/g, "[^/]+");
              const regex = new RegExp(`^${normalizedSubModuleUrl}$`);
              return regex.test(currentPath.pathname);
            });

      const permission = perm?.length
        ? perm?.sort(
            (a: { sub_module_url_user: string }, b: { sub_module_url_user: string }) =>
              b.sub_module_url_user.length - a.sub_module_url_user.length
          )[0]
        : perm;

      if (permission) {
        setPermissions((prev) => ({
          ...prev,
          read: permission?.permission_for_user?.read,
          create: permission?.permission_for_user?.create,
          update: permission?.permission_for_user?.update,
          delete: permission?.permission_for_user?.delete,
          adminData: adminData
        }));

        setIsLoading(false);
      } else {
        setPermissions((prev) => ({
          ...prev,
          read: false,
          create: false,
          update: false,
          delete: false,
          adminData: adminData
        }));
        setIsLoading(false);
      }
    }
  }, [location.pathname, disableChildAuth, parent_url]);
  useEffect(() => console.log(""), [permissions]);
  useEffect(() => console.log(""), []);

  return { permissions, isLoading };
};

const ProtectedRoute = ({
  children,
  disablePageLevelAuth = false,
  disableChildAuth = false,
  parent_url = ""
}: {
  children: (permissions: any) => JSX.Element;
  disablePageLevelAuth?: boolean;
  disableChildAuth?: boolean;
  parent_url?: string;
}) => {
  const { permissions, isLoading } = useSecureRoutePermissions({ disableChildAuth, parent_url });

  if (isLoading) return <LoadingOutlined />;

  // Check if user is root admin or page level auth is disabled
  const isAuthorized = +permissions.adminData?.admin_role_id === 1 || disablePageLevelAuth;

  if (isAuthorized) {
    return children(permissions);
  }

  // Check if user has read permission
  if (!permissions.read) {
    return <Navigate to="/unauthorized" replace />;
  }

  return children(permissions);
};

export { ProtectedRoute, useSecureRoutePermissions };
