export const technicianFeedbackForm = {
  originalFields: [
    {
      id: "interview_completed",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "d5gq5",
            text: "Interview",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "6318d3f7-12c5-4dc3-8fa1-43750b8bb23d",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "d09a9d95-bad8-49de-9111-6397023307ff",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "document_verification",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "f6jpv",
            text: "Document Verification",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 13,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 13,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 13,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 13,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "79782bbc-3711-43da-85c0-5f2979a86354",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "94e6036b-7482-4f96-82e0-5405d86cbef0",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_assessment",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "9lp8d",
            text: "App Assessment",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 14,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 14,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 14,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 14,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "17209ef4-4c7c-42cf-a6c6-e8ec43058a53",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "6ee083c5-578e-466e-b3eb-a1693d244e4e",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "training_completed",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "1ahht",
            text: "Training",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 8,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 8,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 8,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 8,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "4f447ad1-a1df-4b7c-ae7a-70eae711ce97",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "b7b7bbf8-6926-4640-9afb-a7235db3c4ac",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "soft_skills_training",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "8dqrv",
            text: "Soft Skills Training",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 20,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 20,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 20,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 20,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "7d6441b5-7e47-4aa9-b06e-39cd26a4224e",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "0c49ae28-fb0a-462d-a53f-124699e64f28",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "tshirt_bag_handed",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "8fqd1",
            text: "T-shirt and Bag Handed Over",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 27,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 27,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 27,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 27,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "11e6bc63-70c2-4b7b-8844-047cc3c5dc4c",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "124cd854-4ebc-4236-9261-0e3efe214bfe",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "toolkit_assigned",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "4eshs",
            text: "Toolkit Assigned",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 16,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 16,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 16,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 16,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "efcd7c50-10b7-43e8-aa37-71840c30f18c",
          label: "Yes",
          value: "true",
          checked: false
        },
        {
          id: "fc38d79e-ceb8-4afa-bba8-c974fcddab4f",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "app_training",
      element: "RadioButtons",
      required: true,
      label: {
        blocks: [
          {
            key: "8suc9",
            text: "App Training",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [
              {
                offset: 0,
                length: 12,
                style: "color-rgb(55,65,81)"
              },
              {
                offset: 0,
                length: 12,
                style: "bgcolor-rgb(249,250,251)"
              },
              {
                offset: 0,
                length: 12,
                style: "fontsize-14"
              },
              {
                offset: 0,
                length: 12,
                style:
                  'fontfamily--apple-system, "system-ui", "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji'
              }
            ],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      options: [
        {
          id: "67045ea1-a114-4c86-ac47-ce020bf60cd4",
          label: "Yes ",
          value: "true",
          checked: false
        },
        {
          id: "ad2cc452-d9af-4654-911f-02c62bc991bb",
          label: "No",
          value: "false",
          checked: false
        }
      ],
      initialText: "",
      lambdaArn: ""
    },
    {
      id: "onboarding-rating",
      element: "Rating",
      required: true,
      label: {
        blocks: [
          {
            key: "4johh",
            text: "Please rate your onboarding experience",
            type: "unstyled",
            depth: 0,
            inlineStyleRanges: [],
            entityRanges: [],
            data: {}
          }
        ],
        entityMap: {}
      },
      value: 0,
      numberOfStars: 5,
      initialText: "",
      lambdaArn: ""
    }
  ],
  translatedFields: [
    {
      key: "interview_completed",
      required: true,
      label: "Interview",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "document_verification",
      required: true,
      label: "Document Verification",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "app_assessment",
      required: true,
      label: "App Assessment",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "training_completed",
      required: true,
      label: "Training",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "soft_skills_training",
      required: true,
      label: "Soft Skills Training",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "tshirt_bag_handed",
      required: true,
      label: "T-shirt and Bag Handed Over",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "toolkit_assigned",
      required: true,
      label: "Toolkit Assigned",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "app_training",
      required: true,
      label: "App Training",
      widget: "radio-group",
      forwardRef: true,
      options: [
        {
          label: "Yes",
          value: true
        },
        {
          label: "No",
          value: false
        }
      ]
    },
    {
      key: "onboarding-rating",
      required: true,
      label: "Please rate your onboarding experience",
      cust_widget: "Rating",
      widgetProps: {
        count: 5
      }
    }
  ]
};
