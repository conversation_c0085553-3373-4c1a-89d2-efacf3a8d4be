import { Card, Modal, Segmented } from "antd";
import { useState } from "react";
import { HiMiniSquaresPlus } from "react-icons/hi2";
import { LuFilePlus2 } from "react-icons/lu";
import { useNavigate } from "react-router-dom";
import NotAuthorized from "../../components/NotAuthorized";
import CreateNewModule from "./CreateNewModule";
import CreateNewPage from "./CreateNewPage";

type FeatureType = "Module" | "Page";
const NewFeature = () => {
  const navigate = useNavigate();
  const [isWarningVisible, setIsWarningVisible] = useState(true);

  const adminToken = localStorage.getItem("adminToken");
  const [featureType, setFeatureType] = useState<FeatureType>("Module");
  return !adminToken ? (
    <NotAuthorized />
  ) : (
    <div className="min-h-screen bg-purple-50 py-6 px-4 flex flex-col items-center">
      <Card className="w-full max-w-3xl border-l-0 border-r-0 border-t-8 border-b-0 border-purple-500">
        <div className="text-xl mt-2 flex  items-center p-2 ">
          <div className="flex">
            <Segmented
              value={featureType}
              onChange={(value) => setFeatureType(value as FeatureType)}
              options={[
                {
                  label: (
                    <div className="flex p-3">
                      <HiMiniSquaresPlus className=" text-purple-500 text-2xl  mr-3" />{" "}
                      <span className="font-semibold">Create New Module</span>
                    </div>
                  ),
                  value: "Module"
                },
                {
                  label: (
                    <div className="flex p-3">
                      <LuFilePlus2 className=" text-purple-500 text-2xl  mr-3" />{" "}
                      <span className="font-semibold">Create New Page</span>
                    </div>
                  ),
                  value: "Page"
                }
              ]}
            />
          </div>
        </div>
        {featureType === "Module" ? <CreateNewModule /> : <CreateNewPage />}
      </Card>

      <div className="text-gray-500 mt-6 text-xs">All rights reserved © WIFY</div>
      <Modal
        title="🚨 Developers-only Zone"
        open={isWarningVisible}
        onOk={() => setIsWarningVisible(false)}
        onCancel={() => navigate(-1)}
        okText="I Understand"
        cancelText="Go Back"
        closable={false}
        maskClosable={false}
      >
        <p>
          You’re in a monitored environment. All interactions are recorded. Use this page as
          intended.
        </p>
      </Modal>
    </div>
  );
};

export default NewFeature;
