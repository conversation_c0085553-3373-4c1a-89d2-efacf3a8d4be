import { <PERSON><PERSON>, <PERSON>, Col, Divider, Dropdown, Form, Layout, message, Row, Spin } from "antd";
import FormBuilder from "antd-form-builder";
import { useForm } from "antd/es/form/Form";
import React, { useEffect, useState } from "react";
import { RiArrowDownSLine } from "react-icons/ri";
import { useMediaQuery } from "react-responsive";
import { useParams } from "react-router-dom";
import {
  useGetUserChecklistDataQuery,
  useUpdateTechnicianFeedbackDataMutation
} from "../../__generated__";
import { createAntdFormMeta } from "../../utils/formbuilder.helper";
import { technicianFeedbackForm } from "../../utils/technicianFeedbackUtils";
import TechnicianFeedbackSubmitted from "./SubmittedForm";
const { Content } = Layout;

// //map for checklist id from form and checklist name from db checklist

interface FieldType {
  key: string;
  label?: string;
  [key: string]: any;
}
const TechnicianFeedback: React.FC = () => {
  const [meta, setMeta] = useState<FieldType[]>([]);
  const [form] = useForm();

  const params = useParams();
  const userId = Number(params.user_id);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [language, setLanguage] = useState<string>("en");
  const [isAlreadySubmitted, setIsAlreadySubmitted] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  // Query to check if user has already submitted feedback
  const { loading: checklistLoading } = useGetUserChecklistDataQuery({
    variables: { userId },
    onCompleted: (data) => {
      // Check if any technician feedback is already completed
      const hasCompletedFeedback = data?.getUserChecklistData?.data?.some((checklist) =>
        checklist?.user_checklist_data?.some((item) => item?.technician_completed)
      );

      if (hasCompletedFeedback) {
        setIsSubmitted(true);
        setIsAlreadySubmitted(true);
      }
      setIsLoading(false);
    },
    onError: () => {
      setIsLoading(false);
    }
  });

  // Initialize the mutation hook
  const [updateTechnicianFeedbackData, { loading: updateLoading }] =
    useUpdateTechnicianFeedbackDataMutation({
      onCompleted: (data) => {
        if (data?.updateTechnicianFeedbackData?.result) {
          message.success("Feedback submitted successfully!");
          setIsSubmitted(true);
        } else {
          message.error(data?.updateTechnicianFeedbackData?.message || "Failed to submit feedback");
        }
      },
      onError: (error) => {
        message.error(`Error: ${error.message}`);
      }
    });

  const languages = [
    { code: "en", name: "English" },
    { code: "hi", name: "हिंदी" }
  ];

  useEffect(() => {
    try {
      const formMeta = JSON.stringify(technicianFeedbackForm);
      const meta = createAntdFormMeta(formMeta) as FieldType[];
      setMeta(meta);
    } catch (error) {
      console.error("Error parsing technician feedback template:", error);
    }
  }, [form]);

  const handleLanguageChange = (value: string) => {
    setLanguage(value);
  };

  const mapMetaToBooleanWithLabels = (meta: Record<string, string>) => {
    const result: Record<string, { value: boolean | number; label: string }> = {};
    for (const field of technicianFeedbackForm.originalFields) {
      const selectedId = meta[field.id];
      result[field.id] = {
        value: field.id === "onboarding-rating" ? Number(selectedId) : !!selectedId,
        label: field.id === "onboarding-rating" ? "Rating" : field.label.blocks[0].text
      };
    }
    return result;
  };
  const handleSubmit = (values: any) => {
    const parsedMeta = mapMetaToBooleanWithLabels(values);
    const formData = {
      user_id: Number(params.user_id),
      meta: parsedMeta
    };
    updateTechnicianFeedbackData({
      variables: {
        data: formData
      }
    });
  };

  if (isLoading || checklistLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Spin size="large" tip="Loading..." />
      </div>
    );
  }

  if (isSubmitted) {
    return <TechnicianFeedbackSubmitted isAlreadySubmitted={isAlreadySubmitted} />;
  }
  return (
    <Layout className="min-h-screen w-screen bg-gray-50" style={{ userSelect: "none" }}>
      <Content className="pt-8 px-2  pb-12 overflow-y-auto h-full">
        <Row justify="center">
          <Col xs={24} sm={22} md={20} lg={18} xl={16}>
            <Card
              className="shadow-md"
              style={{ borderRadius: "8px" }}
              title={
                <div
                  style={{
                    textAlign: "center",
                    padding: "16px 0",
                    borderBottom: "1px solid #f0f0f0",
                    width: "100%"
                  }}
                >
                  {" "}
                  <Dropdown
                    menu={{
                      items: languages.map((lang) => ({
                        key: lang.code,
                        label: lang.name,
                        onClick: () => handleLanguageChange(lang.code),
                        style:
                          language === lang.code
                            ? { backgroundColor: "#faf5ff ", minWidth: "120px", maxWidth: "120px" }
                            : { minWidth: "120px", maxWidth: "120px" }
                      }))
                    }}
                    className=" flex justify-end"
                    dropdownRender={(menu) => (
                      <div className="p-2 max-w-[120px] items-end">
                        <div className="flex flex-col gap-2">{menu}</div>
                      </div>
                    )}
                    placement=""
                    trigger={["click"]}
                  >
                    <div className="flex items-center justify-end  cursor-pointer">
                      <img src="/icons/language_change_icon.svg" width={25} />
                      <RiArrowDownSLine color="black" />
                    </div>
                  </Dropdown>
                  <div className="flex justify-center items-center mb-2">
                    <div
                      // level={3}
                      style={{
                        margin: 0,
                        marginTop: 20,
                        padding: 0,
                        fontSize: isMobile ? "22px" : "28px",
                        display: "flex",
                        alignItems: "center"
                      }}
                    >
                      {" "}
                      <div className="flex gap-x-1">
                        <span>{language === "en" ? "Welcome to " : ""}</span>
                        <img
                          src="/images/logo.png"
                          alt="WIFY Logo"
                          style={{
                            height: isMobile ? "28px" : "36px",
                            margin: "0 4px",
                            verticalAlign: "middle"
                          }}
                        />{" "}
                        <span>{language === "en" ? "" : "  में आपका स्वागत है !"}</span>
                        <span className="ml-1">🎉</span>
                      </div>
                    </div>
                  </div>
                  <div
                    className="text-center block text-gray-500"
                    style={{
                      fontSize: isMobile ? "14px" : "16px"
                    }}
                  >
                    {language === "en"
                      ? "We're glad to have you on board"
                      : "हमें खुशी है कि आप हमारे साथ जुड़े हैं। "}
                  </div>
                </div>
              }
            >
              <div>
                <Form
                  form={form}
                  layout="horizontal"
                  onFinish={handleSubmit}
                  requiredMark={true}
                  size="small"
                  className=" justify-center "
                >
                  <div className="text-center mb-6 bg-purple-50 p-4 rounded-lg border border-blue-100">
                    <div className="text-lg">
                      {language === "en"
                        ? "Please confirm if the following onboarding steps were completed."
                        : "कृपया सुनिश्चित करें कि आपके, नीचे दिए गए, ऑनबोर्डिंग स्टेप्स पूरे हुए हैं। "}
                    </div>
                  </div>
                  <div className={`${isMobile ? "px-2 " : "px-8 pl-64"}`}>
                    <FormBuilder form={form} meta={meta} />
                  </div>

                  <Divider style={{ marginTop: 36 }} />

                  <div className="flex justify-center gap-x-4">
                    <Form.Item className="text-center" style={{ marginBottom: 0 }}>
                      <Button
                        loading={updateLoading}
                        type="primary"
                        htmlType="submit"
                        size="large"
                        className="hover:shadow-md"
                        style={{
                          minWidth: isMobile ? "120px" : "150px",
                          height: "42px",
                          fontSize: "16px",
                          borderRadius: "8px",
                          transition: "all 0.3s ease"
                        }}
                      >
                        {language === "en" ? "Submit Feedback" : "सबमिट करें"}
                      </Button>
                    </Form.Item>
                  </div>
                </Form>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default TechnicianFeedback;
