import { CheckCircle2, <PERSON><PERSON><PERSON><PERSON><PERSON>, FileText } from "lucide-react";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useGetPoliciesWithUserStatusQuery, useUserDetailsQuery } from "../../__generated__";

export function PolicyCard() {
  const navigate = useNavigate();

  const [readCount, setReadCount] = useState(0);
  const [totalPolicies, setTotalPolicies] = useState(0);

  const handlePolicyCardClick = () => {
    navigate("/policies");
  };
  const { data: userDetails } = useUserDetailsQuery();
  const user_id = userDetails?.userDetail?.data?.id;

  const {
    data: policiesData,
    loading,
    error
  } = useGetPoliciesWithUserStatusQuery({
    variables: { userId: user_id ? +user_id : -1 },
    fetchPolicy: "network-only"
  });

  useEffect(() => {
    if (policiesData?.getPoliciesWithUserStatus?.data) {
      const policies = policiesData.getPoliciesWithUserStatus.data;
      setTotalPolicies(policies.length);

      const acceptedPolicies = policies.filter(
        (policy: any) =>
          policy.user_policy_tracking &&
          policy.user_policy_tracking.length > 0 &&
          policy.user_policy_tracking[0].accepted
      );
      setReadCount(acceptedPolicies.length);
    }
  }, [policiesData]);

  const allPoliciesRead = readCount === totalPolicies && totalPolicies > 0;
  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }
  return (
    <div
      onClick={handlePolicyCardClick}
      className="bg-white/45 rounded-xl p-4 shadow-sm hover:shadow-md transition-all relative overflow-hidden group cursor-pointer"
    >
      <div className="absolute inset-0 bg-[radial-gradient(#4444ff11_1px,transparent_1px)] [background-size:16px_16px] opacity-40"></div>

      <div className="flex items-center justify-between relative">
        <div className="flex items-center gap-3">
          <div
            className={`w-12 h-12 rounded-xl flex items-center justify-center shadow-lg transition-all ${
              allPoliciesRead
                ? "bg-gradient-to-br from-emerald-500 to-green-600 shadow-green-500/20"
                : "bg-gradient-to-br from-blue-500 to-indigo-600 shadow-blue-500/20"
            } group-hover:scale-105`}
          >
            <FileText className="h-6 w-6 text-white" />
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold text-gray-800">Company Policy</h3>
              {allPoliciesRead ? (
                <span className="bg-emerald-100 text-emerald-600 text-xs font-medium px-2 py-0.5 rounded-full flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3" />
                  All Policies Read
                </span>
              ) : (
                <span className="bg-amber-100 text-amber-600 text-xs font-medium px-2 py-0.5 rounded-full">
                  {readCount} of {totalPolicies} Read
                </span>
              )}
            </div>
            <p className="text-sm text-gray-600 mt-0.5">
              {allPoliciesRead
                ? "All company policies have been acknowledged"
                : "View and acknowledge company policies"}
            </p>
          </div>
        </div>
        <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-blue-500 group-hover:translate-x-1 transition-all" />
      </div>
    </div>
  );
}
