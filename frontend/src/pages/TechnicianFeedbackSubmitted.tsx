import { ArrowBackIcon, CheckCircleIcon, ChevronDownIcon } from "@chakra-ui/icons";
import {
  Box,
  Button,
  Container,
  Flex,
  Heading,
  Menu,
  MenuButton,
  MenuItem,
  MenuList,
  Text,
  useColorModeValue,
  VStack
} from "@chakra-ui/react";
import isEmpty from "is-empty";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

const TechnicianFeedbackSubmitted = () => {
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState(i18n.language || "en");
  const navigate = useNavigate();

  const changeLanguage = (lang: string) => {
    i18n.changeLanguage(lang);
    setLanguage(lang);
  };

  // Colors
  const bgColor = useColorModeValue("white", "gray.800");
  const blueBorderColor = useColorModeValue("blue.200", "blue.700");

  const isLoggedIn = !isEmpty(localStorage.getItem("onboarding_token"));

  const isHindi = language === "hi";

  return (
    <>
      <Box minH="100vh" bg={bgColor}>
        <Container maxW="md" px={3} py={4}>
          <Flex justify="space-between" align="center" mb={2}>
            <Button
              leftIcon={<ArrowBackIcon />}
              size="sm"
              variant="outline"
              onClick={() => {
                if (isLoggedIn) {
                  navigate("/profile");
                } else {
                  navigate("/");
                }
              }}
            >
              {isHindi ? "वापस जाएं" : "Back"}
            </Button>
            <Menu>
              <MenuButton as={Button} rightIcon={<ChevronDownIcon />} size="sm" variant="outline">
                <img src="/icons/language_change_icon.svg" width={25} />
              </MenuButton>
              <MenuList>
                <MenuItem
                  onClick={() => changeLanguage("en")}
                  bg={language === "en" ? "purple.50" : "transparent"}
                  fontWeight={language === "en" ? "bold" : "normal"}
                >
                  English
                </MenuItem>
                <MenuItem
                  onClick={() => changeLanguage("hi")}
                  bg={language === "hi" ? "purple.50" : "transparent"}
                  fontWeight={language === "hi" ? "bold" : "normal"}
                >
                  हिंदी
                </MenuItem>
              </MenuList>
            </Menu>
          </Flex>
          <VStack spacing={4} align="stretch">
            <Box borderColor={blueBorderColor} borderRadius="2xl" p={0}>
              <Flex direction="column" align="center" justify="center" mb={4}>
                <Box pt={8} px={2} pb={12} overflowY="auto" h="full">
                  <Flex justify="center">
                    <Box width={{ base: "100%", sm: "90%", md: "80%", lg: "75%", xl: "66%" }}>
                      <Box bg="white" shadow="md" borderRadius="8px" textAlign="center">
                        <Box py={8} px={4}>
                          <CheckCircleIcon w={20} h={20} color="green.500" mb={6} />

                          <Heading as="h2" size="lg" mb={4}>
                            {!isHindi
                              ? "Feedback Already Submitted!"
                              : " प्रतिक्रिया पहले ही भेज दी गई है!"}
                          </Heading>

                          <Text fontSize="16px" display="block" mb={8}>
                            {!isHindi
                              ? "Thank you for providing your valuable feedback. Your input has been recorded."
                              : "आपकी कीमती प्रतिक्रिया देने के लिए धन्यवाद। आपकी राय दर्ज कर ली गई है।"}
                          </Text>
                        </Box>
                      </Box>
                    </Box>
                  </Flex>
                </Box>
              </Flex>
            </Box>
          </VStack>
        </Container>
      </Box>
    </>
  );
};

export default TechnicianFeedbackSubmitted;
