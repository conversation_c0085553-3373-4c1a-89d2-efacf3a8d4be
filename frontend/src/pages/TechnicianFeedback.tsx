import { ArrowBackIcon } from "@chakra-ui/icons";
import { <PERSON>, Button, Container, Flex, useColorModeValue, VStack } from "@chakra-ui/react";
import isEmpty from "is-empty";
import { useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useGetFeedbackFormStatusQuery } from "../__generated__";
import TechnicianFeedbackSubmitted from "./TechnicianFeedbackSubmitted";

const TechnicianFeedback = () => {
  const params = useParams();
  const userId = params.user_id;
  const navigate = useNavigate();
  const [isAlreadySubmitted, setIsAlreadySubmitted] = useState<boolean>(false);

  // Colors
  const bgColor = useColorModeValue("white", "gray.800");
  const blueBorderColor = useColorModeValue("blue.200", "blue.700");

  // URL for the feedback form in admin
  const feedbackUrl = `${import.meta.env.VITE_ADMIN_LINK || ""}/feedbacks/technician-feedback/${userId}`;
  const isLoggedIn = !isEmpty(localStorage.getItem("onboarding_token"));

  const { loading: feedbackStatusLoading } = useGetFeedbackFormStatusQuery({
    variables: { userId: Number(userId) },
    onCompleted: (data) => {
      setIsAlreadySubmitted(data?.getFeedbackFormStatus?.submitted);
    },
    onError: () => {
      // setIsLoading(false);
    }
  });
  if (feedbackStatusLoading) {
    return <div>Loading...</div>;
  }
  if (isAlreadySubmitted) {
    return <TechnicianFeedbackSubmitted />;
  }

  return (
    <>
      <Box minH="100vh" bg={bgColor}>
        <Container maxW="md" px={3} py={4}>
          <Flex justify="space-between" align="center" mb={2}>
            <Button
              leftIcon={<ArrowBackIcon />}
              size="sm"
              variant="outline"
              onClick={() => {
                if (isLoggedIn) {
                  navigate("/profile");
                } else {
                  navigate("/");
                }
              }}
            >
              Back
              {/* {isHindi ? "वापस जाएं" : "Back"} */}
            </Button>
          </Flex>
          <VStack spacing={1} align="stretch">
            <Box borderColor={blueBorderColor} borderRadius="2xl" p={0}>
              <iframe
                src={feedbackUrl}
                style={{ width: "100%", height: "100vh", border: "none" }}
                title="Technician Feedback Form"
              />
            </Box>
          </VStack>
        </Container>
      </Box>
    </>
  );
};

export default TechnicianFeedback;
